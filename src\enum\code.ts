/** WebSocket 发送消息类型枚举 */
export enum SendFunctionCode {
  心跳 = 1,
  登录 = 10000,
  请求TOKEN = 11002,
  登出 = 19999,
}

/** WebSocket 接收消息类型枚举 */
export enum ReceiveFunctionCode {
  登录响应 = 20000,
  请求TOKEN相应 = 20008,
  新增策略池推送 = 70001,
  更新策略池推送 = 70002,
  删除策略池推送 = 70003,
  策略池明细变更推送 = 70004,
  // 卖单策略变更推送 = 70005,
}

/**
 * WebSocket 连接状态枚举
 */
export enum ConnectionStatus {
  断连 = 'disconnected', // 连接已断开
  连接中 = 'connecting', // 正在建立连接
  已连接 = 'connected', // 连接已建立
  重连中 = 'reconnecting', // 正在尝试重新连接
}
