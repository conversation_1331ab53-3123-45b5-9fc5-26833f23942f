html {
  margin: 0;
  width: 100%;
  height: 100%;
  background-color: var(--g-bg);
  body {
    margin: 0;
    width: 100%;
    height: 100%;
    color: var(--g-panel-text);
    font-size: 12px;
    #app {
      margin: 0;
      width: 100%;
      height: 100%;
    }

    /* element ui */
    --el-text-color-placeholder: var(--g-panel-text);
    --el-text-color-regular: white;
    --el-bg-color-overlay: var(--g-bg);
    --el-border-color: var(--g-bg);
    --el-border-color-hover: var(--g-bg-hover);
    --el-font-size-base: 12px;
    --el-component-size: 28px;
    .el-scrollbar {
      --el-scrollbar-opacity: 0.7;
      .el-scrollbar__thumb {
        background-color: rgb(124, 152, 187);
        &:hover {
          opacity: 0.9;
        }
      }
    }

    .el-menu {
      --el-menu-bg-color: var(--g-panel-bg2);
      --el-menu-border-color: var(--g-panel-bg2);
      --el-menu-text-color: var(--g-panel-text);
      --el-menu-hover-bg-color: var(--g-bg);
      --el-menu-item-height: 30px;
      --el-menu-sub-item-height: 30px;
      --el-menu-item-font-size: 14px;
      .el-menu-item {
        &:hover {
          color: white;
        }

        &.is-active {
          color: white;
          background-color: var(--g-primary);
        }
      }

      .el-sub-menu {
        .el-sub-menu__title {
          &:hover {
            color: white;
          }
        }
      }
    }

    .el-tabs {
      --el-tabs-header-height: 32px;
      background-color: var(--g-panel-bg2);
      padding-left: 12px;
      .el-tabs__content {
        display: none;
      }
      .el-tabs__header {
        margin-bottom: 0;
        border-bottom: none;

        .el-tabs__nav-wrap {
          margin-bottom: 0;
          .el-tabs__nav-scroll {
            .el-tabs__nav {
              border: none;
              border-radius: 0;
              .el-tabs__item {
                border: none;
                border-right: 1px solid var(--g-panel-bg);
                color: var(--g-panel-text);
                height: 30px;
                border-top-right-radius: 2px;
                border-top-left-radius: 2px;
                padding-left: 10px;
                padding-right: 10px;
                font-size: 14px;
                &:hover {
                  .is-icon-close {
                    opacity: 1;
                  }
                }
                &.is-active {
                  color: white;
                  background-color: var(--g-primary);
                }
                .is-icon-close {
                  width: 16px;
                  height: 16px;
                  opacity: 0;
                  &:hover {
                    background-color: var(--g-primary);
                  }
                  svg {
                    width: 100% !important;
                    height: 100% !important;
                  }
                }
              }
            }
          }
        }
        .el-tabs__new-tab {
          display: none;
        }
      }
    }

    .el-select {
      --el-select-font-size: 12px;
      --el-select-input-font-size: 12px;
      --el-select-border-color-hover: transparent;
      --el-select-disabled-border: transparent;
      --el-select-input-color: white;
      --el-select-multiple-input-color: white;
      --el-select-close-hover-color: white;
      .el-select__wrapper {
        font-size: 12px;
        background-color: var(--g-bg);
        line-height: 28px;
        min-height: 28px;
        /* box-shadow: var(--g-input-shadow);
        &.is-hovering {
          &:not(.is-focused) {
            box-shadow: var(--g-input-shadow);
          }
        }
        &.is-focused {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        } */
        /* .el-select__selection {
        } */
        /* .el-select__suffix {
        } */
      }
    }
    .el-select-dropdown {
      .el-select-dropdown__item {
        font-size: 12px;
        &.is-selected,
        &.is-hovering {
          background-color: var(--g-hover);
        }
      }
    }

    .el-popper {
      &.is-light {
        border: none;
      }
      &[data-popper-placement='bottom-start'] {
        .el-popper__arrow {
          &::before {
            border-color: transparent;
          }
        }
      }
    }

    .el-input {
      .el-input__wrapper {
        background-color: var(--g-bg);
        /* box-shadow: var(--g-input-shadow);
        &.is-hovering {
          &:not(.is-focused) {
            box-shadow: var(--g-input-shadow);
          }
        }
        &.is-focused {
          box-shadow: 0 0 0 1px var(--el-color-primary);
        } */
      }
    }

    .el-input-number {
      --el-fill-color-light: var(--g-panel-bg3);
      .el-input-number__decrease {
        border-right-color: var(--g-bg);
      }
      .el-input-number__increase {
        border-left-color: var(--g-bg);
      }
      .el-input-number__decrease,
      .el-input-number__increase {
        &.is-disabled {
          background-color: var(--g-panel-bg);
        }
      }
    }

    .el-table {
      --el-table-border-color: transparent;
      --el-table-header-bg-color: var(--g-panel-bg2);
      --el-table-header-text-color: var(--g-header-text);
      --el-table-row-hover-bg-color: var(--g-panel-bg3);
      --el-table-tr-bg-color: var(--g-panel-bg2);
      --el-table-tr-bg-color-hover: var(--g-panel-bg3);
    }
    .el-table-v2 {
      --el-table-border-color: transparent;
      --el-table-header-bg-color: var(--g-panel-bg2);
      --el-table-header-text-color: var(--g-header-text);
      --el-table-row-hover-bg-color: var(--g-panel-bg3);
      .el-table-v2__table {
        background-color: var(--g-panel-bg2);
        .el-table-v2__header-wrapper {
          background-color: var(--g-panel-bg2);
          .el-table-v2__header {
            .el-table-v2__header-row {
              .el-table-v2__header-cell {
                .el-table-v2__sort-icon {
                  line-height: unset;
                }
              }
            }
          }
        }
        .el-table-v2__body {
          .el-table-v2__row {
            color: white;
          }
        }
      }
    }
  }
}

/*滚动条样式*/
::-webkit-scrollbar {
  /*滚动条整体样式*/
  width: 5px;
  height: 5px;
  position: absolute;
}
::-webkit-scrollbar-track {
  /*滚动条里面轨道*/
  background: transparent;
}
::-webkit-scrollbar-thumb {
  /*滚动条里面小方块*/
  background: #b6b9dd;
  border-radius: 10px;
  box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
}

::-webkit-scrollbar-corner {
  background: transparent;
  border: none;
}
