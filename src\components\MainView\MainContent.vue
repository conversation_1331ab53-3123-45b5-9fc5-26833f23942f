<script setup lang="ts">
import { computed, defineAsyncComponent } from 'vue';
import { TABS } from '@/enum';

const { activeTab } = defineProps<{
  activeTab?: string;
}>();

const components: Record<string, unknown> = {};

TABS.forEach(menu => {
  components[menu.component] = defineAsyncComponent(
    () => import(`./MainContent/${menu.component}.vue`),
  );
});

const componentIns = computed(() => components[activeTab!]);
</script>

<template>
  <KeepAlive>
    <component :is="componentIns"></component>
  </KeepAlive>
</template>

<style scoped></style>
