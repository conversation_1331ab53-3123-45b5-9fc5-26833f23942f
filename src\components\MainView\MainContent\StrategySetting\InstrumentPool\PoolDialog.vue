<script setup lang="ts">
import { ref, useTemplateRef, watch } from 'vue';
import type { FormPool, Pool } from '@/types';
import { PoolService } from '@/api';
import { ElMessage } from 'element-plus';

const { pool } = defineProps<{
  pool?: Pool;
}>();

const visible = defineModel<boolean>();

const emit = defineEmits<{
  success: [];
}>();

// 表单校验规则
const rules = {
  groupName: [{ required: true, message: '请输入股票池名称', trigger: 'blur' }],
  positionRate: [{ required: true, message: '请输入仓位', trigger: 'blur' }],
};

const formRef = useTemplateRef('formRef');

const form = ref<FormPool>({
  groupName: '',
  positionRate: 5,
});

// 监听visible变化
watch(visible, val => {
  if (val) {
    if (pool) {
      form.value = {
        groupName: pool.groupName,
        positionRate: pool.positionRate,
      };
    }
  }
});

// 提交表单
const handleSubmit = () => {
  formRef.value?.validate(async valid => {
    if (valid) {
      if (pool) {
        const { errorCode, errorMsg } = await PoolService.updateStrategyPool({
          ...pool,
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success');
          ElMessage.success('修改成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      } else {
        const { errorCode, errorMsg } = await PoolService.addStrategyPool({
          ...form.value,
        });
        if (errorCode === 0) {
          emit('success');
          ElMessage.success('添加成功');
          handleClose();
        } else {
          ElMessage.error(errorMsg || '操作失败');
        }
      }
    }
  });
};

// 关闭弹窗
const handleClose = () => {
  visible.value = false;
  form.value = {
    groupName: '',
    positionRate: 5,
  };
  formRef.value?.resetFields();
};
</script>
<template>
  <el-dialog
    :model-value="visible"
    :title="pool ? '修改股票池' : '新建股票池'"
    width="300px"
    @close="handleClose"
  >
    <el-form ref="formRef" :model="form" :rules="rules" label-width="90px">
      <el-form-item label="股票池名称" prop="groupName">
        <el-input v-model="form.groupName" placeholder="请输入股票池名称" />
      </el-form-item>
      <el-form-item w-full label="仓位" prop="positionRate">
        <el-input-number
          important-w-full
          v-model="form.positionRate"
          :min="0"
          :max="100"
          :precision="0"
          :step="1"
          placeholder="请输入仓位"
        >
          <template #suffix>%</template>
        </el-input-number>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-button @click="handleClose">取消</el-button>
      <el-button type="primary" @click="handleSubmit">确定</el-button>
    </template>
  </el-dialog>
</template>
