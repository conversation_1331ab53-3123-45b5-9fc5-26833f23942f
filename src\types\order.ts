import type {
  BusinessFlagEnum,
  HedgeFlagEnum,
  OrderPriceTypeEnum,
  TradeDirectionEnum,
} from '@/enum';

export interface TableOrderInfo {
  /** 订单 ID */
  id: number;
  /** 父订单 ID */
  parentOrderId: number;
  /** 用户 ID */
  userId: number;
  /** 用户姓名 */
  userName: string;
  /** 基金 ID */
  fundId: number;
  /** 基金名称 */
  fundName: string;
  /** 账户 ID */
  accountId: number;
  /** 资金账户 */
  financeAccount: string;
  /** 账户名称 */
  accountName: string;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 冻结保证金 */
  frozenMargin: number;
  /** 冻结手续费 */
  frozenCommission: number;
  /** 冻结数量 */
  frozenVolume: number;
  /** 成交金额 */
  tradedAmount: number;
  /** 成交数量 */
  tradedVolume: number;
  /** 成交价格 */
  tradedPrice: number;
  /** 撤单数量 */
  cancelledVolume: number;
  /** 原委托数量 */
  volumeOriginal: number;
  /** 手续费 */
  commission: number;
  /** 是否境外 */
  foreign: boolean;
  /** 资产类型 */
  assetType: number;
  /** 订单状态 */
  orderStatus: number;
  /** 本地订单 ID */
  localOrderId: string;
  /** 交易所订单 ID */
  exchangeOrderId: string;
  /** 委托价格 */
  orderPrice: number;
  /** 委托价格类型 */
  orderPriceType: number;
  /** 买卖方向 */
  direction: number;
  /** 开平仓标志 */
  positionEffect: number;
  /** 投机套保标识 */
  hedgeFlag: number;
  /** 是否强平 */
  forceClose: boolean;
  /** 委托时间 */
  orderTime: string;
  /** 交易日 */
  tradingDay: string;
  /** 自定义 ID */
  customId: number;
  /** 业务标志 */
  businessFlag: number;
}

/** 下单时的参数 */
export interface TradeOrderInfo {
  accountId: number;
  bsFlag: TradeDirectionEnum;
  businessFlag: BusinessFlagEnum;
  customId: string;
  hedgeFlag: HedgeFlagEnum;
  instrument: string;
  orderTime: number;
  positionEffect: number;
  price: number;
  priceType: OrderPriceTypeEnum;
  strategyId: number;
  userId: number;
  volume: number;
}
