<script setup lang="tsx">
import type { ColumnDefinition, RowAction, Pool } from '@/types';
import { shallowRef, onMounted } from 'vue';
import { Misc, Utils } from '@/script';
import { ElMessage, ElMessageBox } from 'element-plus';
import { PoolService } from '@/api';
import { TacticStatusEnum, PoolTypeEnum, isStopped } from '@/enum';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import PoolDialog from './InstrumentPool/PoolDialog.vue';
import { usePoolSelectionStore } from '@/stores';

const columns: ColumnDefinition<Pool> = [
  {
    key: 'groupName',
    dataKey: 'groupName',
    title: '票池名称',
    width: 200,
  },
  {
    key: 'groupType',
    dataKey: 'groupType',
    title: '类型',
    width: 200,
    cellRenderer: ({ cellData }) => {
      return <span>{PoolTypeEnum[cellData]}</span>;
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '任务启停',
    width: 150,
    cellRenderer: ({ rowData }: { rowData: Pool }) => {
      return (
        <el-switch
          modelValue={isStopped(rowData) ? TacticStatusEnum.已停止 : TacticStatusEnum.运行中}
          active-value={1}
          inactive-value={0}
          before-change={() => beforeChange(rowData)}
          onClick={(e: Event) => e.stopPropagation()}
        />
      );
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '状态',
    width: 150,
    cellRenderer: ({ cellData }) => {
      const text = TacticStatusEnum[cellData];
      return <span>{text}</span>;
    },
  },
  {
    key: 'size',
    dataKey: 'size',
    title: '个股数',
    width: 150,
  },
  {
    key: 'positionRate',
    dataKey: 'positionRate',
    title: '仓位',
    width: 200,
    minWidth: 150,
    cellRenderer: ({ cellData }) => {
      return <span>{Utils.formatNumber(cellData)}%</span>;
    },
  },
];

const rowActions: RowAction<Pool>[] = [
  {
    label: '修改',
    onClick: row => {
      selectedPool.value = row;
      visible.value = true;
    },
    color: 'var(--g-primary)',
  },
  {
    label: '删除',
    onClick: row => {
      ElMessageBox.confirm(`确认删除 ${row.groupName}`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      })
        .then(async () => {
          const { errorCode, errorMsg } = await PoolService.deleteStrategyPool(row.id);
          if (errorCode === 0) {
            ElMessage.success('删除成功');
            monitorInfos.value = monitorInfos.value.filter(x => x.id !== row.id);
          } else {
            ElMessage.error(errorMsg || '删除失败');
          }
        })
        .catch(() => {});
    },
    color: 'var(--g-red)',
  },
];

const monitorInfos = shallowRef<Pool[]>([]);
const visible = shallowRef(false);
const { setSelectedPool } = usePoolSelectionStore();
const selectedPool = shallowRef<Pool | undefined>();

onMounted(() => {
  getData();
});

/** 点击行时，设置选中股票池 */
const handleRowClick = (row: Pool) => {
  setSelectedPool(row);
};

const getData = async () => {
  const { errorCode, data } = await PoolService.getStrategyPools();
  if (errorCode === 0 && data) {
    monitorInfos.value = data;
  }
};

const handleStart = async () => {
  const { errorCode, errorMsg } = await PoolService.startStrategyPool();
  if (errorCode === 0) {
    ElMessage.success('启动成功');
    getData();
  } else {
    ElMessage.error(errorMsg || '启动失败');
  }
};
const handleStop = async () => {
  const { errorCode, errorMsg } = await PoolService.stopStrategyPool();
  if (errorCode === 0) {
    ElMessage.success('停止成功');
    getData();
  } else {
    ElMessage.error(errorMsg || '停止失败');
  }
};
const handleAdd = () => {
  selectedPool.value = undefined;
  visible.value = true;
};
/** 进行启停操作，成功返回true,否则返回false */
const beforeChange = async (rowData: Pool) => {
  const { errorCode, errorMsg } = await PoolService[
    isStopped(rowData) ? 'startStrategyPool' : 'stopStrategyPool'
  ](rowData.id);
  if (errorCode === 0) {
    Misc.putRow(
      {
        ...rowData,
        status: isStopped(rowData) ? TacticStatusEnum.运行中 : TacticStatusEnum.已停止,
      },
      monitorInfos,
    );
    return true;
  } else {
    ElMessage.error(errorMsg || '操作失败');
    return false;
  }
};

defineExpose({
  getData,
});
</script>

<template>
  <div flex="~ col">
    <div flex aic jcsb px-16 h-32>
      <div text-14 font-bold>股票池</div>
      <div>
        <el-button color="var(--g-bg-green-l)" @click="handleStart">
          <i mr-4 fs-14 i-mdi-motion-play-outline />
          一键启动
        </el-button>
        <el-button color="var(--g-red-l)" @click="handleStop">
          <i mr-4 fs-14 i-mdi-motion-pause-outline />
          一键停止
        </el-button>
        <el-button color="var(--g-primary)" @click="handleAdd">
          <i mr-4 fs-14 i-mdi-plus />
          添加
        </el-button>
      </div>
    </div>
    <VirtualizedTable
      flex-1
      min-h-1
      :data="monitorInfos"
      :columns="columns"
      :row-actions="rowActions"
      :row-action-width="130"
      @row-click="handleRowClick"
    ></VirtualizedTable>
    <PoolDialog v-model="visible" :pool="selectedPool" @success="getData" />
  </div>
</template>

<style scoped></style>
