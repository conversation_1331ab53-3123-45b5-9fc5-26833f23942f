<script setup lang="tsx">
import type { ColumnDefinition, InstrumentInfo, PoolDetail, RowAction } from '@/types';
import { ref, shallowRef, useTemplateRef, watch } from 'vue';
import { PoolService, MarketService } from '@/api';
import { usePoolSelectionStore } from '@/stores';
import { storeToRefs } from 'pinia';
import VirtualizedTable from '@/components/common/VirtualizedTable.vue';
import { AssetTypeEnum, TacticStatusEnum } from '@/enum';
import { ElMessage, ElMessageBox } from 'element-plus';
import InstrumentInput from '@/components/common/InstrumentInput.vue';
import * as XLSX from 'xlsx';

// 表格列定义
const columns: ColumnDefinition<PoolDetail> = [
  {
    key: 'instrument',
    dataKey: 'instrument',
    title: '证券代码',
    width: 200,
  },
  {
    key: 'instrumentName',
    dataKey: 'instrumentName',
    title: '证券名称',
    width: 200,
  },
  {
    key: 'positionRate',
    dataKey: 'positionRate',
    title: '仓位',
    width: 200,
    cellRenderer: ({ cellData }) => {
      return <span>{cellData ? cellData + '%' : '--'}</span>;
    },
  },
  {
    key: 'status',
    dataKey: 'status',
    title: '状态',
    width: 200,
    cellRenderer: ({ cellData }) => {
      const text = TacticStatusEnum[cellData];
      return <span>{text}</span>;
    },
  },
];

const rowActions: RowAction<PoolDetail>[] = [
  {
    label: '删除',
    onClick: row => {
      ElMessageBox.confirm('确认删除', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      }).then(async () => {
        const { errorCode, errorMsg } = await PoolService.deletePoolDetail(row.id);
        if (errorCode === 0) {
          ElMessage.success('删除成功');
          details.value = details.value.filter(x => x.id !== row.id);
        } else {
          ElMessage.error(errorMsg || '删除失败');
        }
      });
    },
    color: 'var(--g-red)',
  },
];

const details = shallowRef<PoolDetail[]>([]);
const { selectedPool } = storeToRefs(usePoolSelectionStore());
const selectedInstrument = ref<InstrumentInfo>();
const positionRate = ref(5);
const instrumentInputRef = useTemplateRef('instrumentInputRef');

// 导入相关变量
const allInstruments = shallowRef<InstrumentInfo[]>([]);
const isImporting = ref(false);

// 获取股票池详情数据
const getDetails = async (poolId: number) => {
  const { errorCode, data } = await PoolService.getPoolDetail(poolId);
  if (errorCode === 0 && data) {
    details.value = data;
  } else {
    details.value = [];
  }
};

// 监听选中股票池变化
watch(
  selectedPool,
  newPool => {
    if (newPool) {
      getDetails(newPool.id);
    } else {
      details.value = [];
    }
  },
  { immediate: true },
);

watch(selectedInstrument, val => {
  if (val) {
    if (!details.value.some(x => x.instrument === val.instrument)) {
      handleAdd();
    }
  }
});

// 获取全量股票列表
const getAllInstruments = async () => {
  if (allInstruments.value.length > 0) return;

  const { errorCode, data } = await MarketService.downloadInstruments(AssetTypeEnum.股票);
  if (errorCode === 0 && data) {
    allInstruments.value = data;
  }
};

/** 手动添加股票 */
const handleAdd = async () => {
  if (!selectedPool.value) {
    ElMessage.error('请先选择股票池');
    return;
  }
  if (!selectedInstrument.value) {
    ElMessage.error('请先选择股票');
    return;
  }
  if (positionRate.value <= 0) {
    ElMessage.error('请先设置仓位');
    return;
  }
  const detail: Omit<PoolDetail, 'id' | 'status'> = {
    instrument: selectedInstrument.value.instrument,
    instrumentName: selectedInstrument.value.instrumentName,
    poolName: selectedPool.value.groupName,
    poolId: selectedPool.value.id,
    positionRate: positionRate.value,
  };
  instrumentInputRef.value?.clear();
  const { errorCode, errorMsg } = await PoolService.addPoolDetail(detail);
  if (errorCode === 0) {
    ElMessage.success('添加成功');
    getDetails(selectedPool.value.id);
  } else {
    ElMessage.error(errorMsg || '添加失败');
  }
};

// 下载导入模板
const handleDownloadTemplate = () => {
  const link = document.createElement('a');
  link.href = '/股票导入模板.xlsx';
  link.download = '股票导入模板.xlsx';
  document.body.appendChild(link);
  link.click();
  document.body.removeChild(link);
};

// 提取股票代码的工具函数
const extractStockCode = (code: string): string | null => {
  if (!code) return null;

  // 移除所有空格和引号
  const cleanCode = code.toString().replace(/[\s'"]/g, '');

  // 匹配6位数字的股票代码
  const match = cleanCode.match(/(\d{6})/);
  return match ? match[1] : null;
};

// 处理导入
const handleImport = async () => {
  if (!selectedPool.value) {
    ElMessage.error('请先选择股票池');
    return;
  }

  // 确保已获取全量股票列表
  await getAllInstruments();

  // 创建文件输入元素
  const input = document.createElement('input');
  input.type = 'file';
  input.accept = '.xlsx,.xls';

  input.onchange = async event => {
    const file = (event.target as HTMLInputElement).files?.[0];
    if (!file) return;

    try {
      isImporting.value = true;

      // 读取Excel文件
      const arrayBuffer = await file.arrayBuffer();
      const workbook = XLSX.read(arrayBuffer);
      const worksheet = workbook.Sheets[workbook.SheetNames[0]];
      const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 }) as string[][];

      if (jsonData.length < 2) {
        ElMessage.error('模板文件格式不正确，至少需要包含标题行和数据行');
        return;
      }

      // 提取股票代码（从第二行开始，第一列）
      const stockCodes: string[] = [];
      for (let i = 1; i < jsonData.length; i++) {
        const row = jsonData[i];
        if (row && row[0]) {
          const code = extractStockCode(row[0]);
          if (code) {
            stockCodes.push(code);
          }
        }
      }

      if (stockCodes.length === 0) {
        ElMessage.error('未找到有效的股票代码');
        return;
      }

      // 构建股票代码到InstrumentInfo的映射
      const instrumentMap = new Map<string, InstrumentInfo>();
      allInstruments.value.forEach(instrument => {
        const code = extractStockCode(instrument.instrument);
        if (code) {
          instrumentMap.set(code, instrument);
        }
      });

      // 过滤有效的股票并排除已存在的
      const existingCodes = new Set(
        details.value.map(d => extractStockCode(d.instrument)).filter(Boolean),
      );
      const validInstruments: InstrumentInfo[] = [];
      const invalidCodes: string[] = [];

      stockCodes.forEach(code => {
        if (existingCodes.has(code)) {
          // 已存在，跳过
          return;
        }

        const instrument = instrumentMap.get(code);
        if (instrument) {
          validInstruments.push(instrument);
        } else {
          invalidCodes.push(code);
        }
      });

      if (validInstruments.length === 0) {
        if (invalidCodes.length > 0) {
          ElMessage.error(`所有股票代码都无效或已存在。无效代码：${invalidCodes.join(', ')}`);
        } else {
          ElMessage.warning('所有股票都已存在于当前股票池中');
        }
        return;
      }

      // 构造PoolDetail对象数组
      const poolDetails: Omit<PoolDetail, 'id' | 'status'>[] = validInstruments.map(instrument => ({
        instrument: instrument.instrument,
        instrumentName: instrument.instrumentName,
        poolName: selectedPool.value!.groupName,
        poolId: selectedPool.value!.id,
        positionRate: positionRate.value,
      }));

      const { errorCode } = await PoolService.addPoolDetails(poolDetails);
      if (errorCode === 0) {
        ElMessage.success('导入成功');
        getDetails(selectedPool.value!.id);
      } else {
        ElMessage.error('导入失败');
      }
    } catch (error) {
      console.error('导入失败:', error);
      ElMessage.error('文件解析失败，请检查文件格式');
    } finally {
      isImporting.value = false;
    }
  };

  input.click();
};

const handleClear = () => {
  if (!selectedPool.value) {
    ElMessage.error('请先选择股票池');
    return;
  }
  ElMessageBox.confirm('确认清空当前股票池的所有股票？', '提示', {
    confirmButtonText: '确定',
    cancelButtonText: '取消',
    type: 'warning',
  }).then(async () => {
    const { errorCode, errorMsg } = await PoolService.clearPoolDetails(selectedPool.value!.id);
    if (errorCode === 0) {
      ElMessage.success('清空成功');
      getDetails(selectedPool.value!.id);
    } else {
      ElMessage.error(errorMsg || '清空失败');
    }
  });
};
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 jcsb>
      <div w-380 flex aic gap-10>
        <div text-14 font-bold>股票池详情</div>
        <InstrumentInput
          flex-1
          min-w-1
          v-model="selectedInstrument"
          :assetType="AssetTypeEnum.股票"
          placeholder="添加股票"
          ref="instrumentInputRef"
        />
        <div flex aic gap-10>
          <div>股票池仓位</div>
          <el-input v-model="positionRate" class="position-input">
            <template #append>%</template>
          </el-input>
        </div>
      </div>
      <div flex aic>
        <el-tooltip effect="light" content="下载导入模板" placement="top">
          <i cursor-pointer mr-10 block fs-18 i-mdi-download @click="handleDownloadTemplate"></i>
        </el-tooltip>
        <el-button color="var(--g-primary)" :loading="isImporting" @click="handleImport">
          <i mr-4 fs-14 i-mdi-import />
          导入
        </el-button>
        <el-button color="var(--g-red)" @click="handleClear">
          <i mr-4 fs-14 i-mdi-trash-can-outline />
          清空
        </el-button>
      </div>
    </div>
    <VirtualizedTable
      showIndex
      :data="details"
      :columns="columns"
      :row-actions="rowActions"
    ></VirtualizedTable>
  </div>
</template>

<style scoped>
.position-input {
  width: 70px;
}
:deep() {
  .el-input-group__append {
    padding: 0 5px;
  }
}
</style>
