<script setup lang="ts">
import { computed, shallowRef, watch } from 'vue';
import { useStockSelectionStore } from '@/stores';
import { storeToRefs } from 'pinia';
import { TickService } from '@/api';
import type { StandardTick, RealtimeTrade } from '@/types';
import { Utils } from '@/script';

const { selectedStock } = storeToRefs(useStockSelectionStore());
const tickData = shallowRef<StandardTick | null>(null);
const realtimeTrades = shallowRef<RealtimeTrade[]>([]);

// 获取行情数据
const fetchTickData = async (instrument: string) => {
  const { errorCode, data } = await TickService.getTick(instrument);
  if (errorCode === 0 && data) {
    tickData.value = data;
  }
};

// 获取实时成交数据
const fetchRealtimeTrades = async (instrument: string) => {
  const { errorCode, data } = await TickService.getRealtimeTrades(instrument);
  if (errorCode === 0 && data) {
    realtimeTrades.value = data;
  }
};

// 监听选中股票变化
watch(
  selectedStock,
  newStock => {
    if (newStock) {
      fetchTickData(newStock.instrument);
      fetchRealtimeTrades(newStock.instrument);
    } else {
      tickData.value = null;
      realtimeTrades.value = [];
    }
  },
  { immediate: true },
);

// 计算涨跌幅和涨跌额
const priceChange = computed(() => {
  if (!tickData.value) return { amount: 0, percent: 0 };
  const amount = tickData.value.lastPrice - tickData.value.preClosePrice;
  const percent = amount / tickData.value.preClosePrice;
  return { amount, percent };
});

// 获取价格颜色
const getPriceColor = (price?: number, comparePrice: number = 0) => {
  if (!price) return '';
  if (price > comparePrice) return 'c-[var(--g-red)]';
  if (price < comparePrice) return 'c-[var(--g-green)]';
  return 'c-white';
};
</script>

<template>
  <div flex="~ col" bg="[--g-panel-bg]" border-b="1 solid [--el-border-color]">
    <!-- 股票信息 -->
    <div h-68 flex="~ col" jcc px-12 border-b="1 solid [--el-border-color]">
      <div flex aic gap-8 mb-6>
        <div fs-16 font-bold>{{ selectedStock?.instrument || '--' }}</div>
        <div fs-16 c-white>{{ selectedStock?.instrumentName || '--' }}</div>
      </div>
      <div flex aic gap-12>
        <div fs-16 font-bold :class="getPriceColor(tickData?.lastPrice, tickData?.preClosePrice)">
          {{ Utils.formatNumber(tickData?.lastPrice, { fix: 2 }) }}
        </div>
        <div :class="getPriceColor(priceChange.amount, 0)">
          {{ Utils.formatNumber(priceChange.amount, { fix: 2, prefix: true }) }}
        </div>
        <div :class="getPriceColor(priceChange.percent, 0)">
          {{ Utils.formatNumber(priceChange.percent, { percent: true, prefix: true }) }}
        </div>
      </div>
    </div>
    <!-- 买卖盘 -->
    <div flex gap-4 flex-1 pl-12 py-8>
      <!-- 左侧买卖盘 -->
      <div flex-1>
        <!-- 卖盘 -->
        <div
          hover="bg-[--g-bg-hover2]"
          v-for="index in 5"
          :key="`ask-${index}`"
          flex
          aic
          jcsb
          h-24
          px-4
        >
          <div w-50>卖{{ 6 - index }}</div>
          <div
            :class="getPriceColor(tickData?.askPrice[5 - index], tickData?.preClosePrice)"
            w-60
            flex="~ justify-end"
          >
            {{ tickData ? Utils.formatNumber(tickData.askPrice[5 - index], { fix: 2 }) : '--' }}
          </div>
          <div flex-1 min-w-1 c-white flex="~ justify-end">
            {{ tickData ? tickData.askVolume[5 - index] : '--' }}
          </div>
        </div>

        <!-- 分隔线 -->
        <div h-1 bg="[--el-border-color]" my-2></div>

        <!-- 买盘 -->
        <div
          v-for="index in 5"
          :key="`bid-${index}`"
          hover="bg-[--g-bg-hover2]"
          flex
          aic
          jcsb
          h-24
          px-4
        >
          <div w-50>买{{ index }}</div>
          <div
            :class="getPriceColor(tickData?.bidPrice[index - 1], tickData?.preClosePrice)"
            w-60
            flex="~ justify-end"
          >
            {{ tickData ? Utils.formatNumber(tickData.bidPrice[index - 1], { fix: 2 }) : '--' }}
          </div>
          <div flex-1 min-w-1 c-white flex="~ justify-end">
            {{ tickData ? tickData.bidVolume[index - 1] : '--' }}
          </div>
        </div>
      </div>
      <!-- 右侧实时成交 -->
      <div flex="~ col" w-180>
        <!-- 成交数据 -->
        <div flex="~ col" max-h-243>
          <el-scrollbar>
            <div
              v-for="(trade, index) in realtimeTrades"
              :key="index"
              flex
              aic
              pl-4
              pr-12
              jcsb
              hover="bg-[--g-bg-hover2]"
            >
              <div w-40>{{ trade.time }}</div>
              <div
                flex="~ justify-end"
                w-50
                :class="trade.direction === 'B' ? 'c-[var(--g-red)]' : 'c-[var(--g-green)]'"
              >
                {{ Utils.formatNumber(trade.price, { fix: 2 }) }}
              </div>
              <div w-30 flex="~ justify-end">{{ trade.direction }}</div>
              <div flex-1 min-w-1 flex="~ justify-end" c-white>{{ trade.volume }}</div>
            </div>
          </el-scrollbar>
        </div>
      </div>
    </div>
  </div>
</template>

<style scoped></style>
