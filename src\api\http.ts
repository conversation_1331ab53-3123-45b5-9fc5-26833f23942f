import axios, { type AxiosRequestConfig } from 'axios';
import qs from 'qs';
import Misc from '../script/misc';
import router from '../router';

const customAxios = axios.create({
  timeout: 1000 * 300,
  withCredentials: false,
  headers: {
    'X-Requested-With': 'XMLHttpRequest',
    Accept: 'application/json',
    'Content-Type': 'application/json',
  },
  transformRequest: [
    (data: object, headers) => {
      if (headers['Content-Type'] === 'application/json') {
        return JSON.stringify(data);
      } else if (headers['Content-Type'] === 'multipart/form-data') {
        return data;
      }
      return qs.stringify(data);
    },
  ],
});

customAxios.interceptors.request.use(
  config => {
    if (!config.params) {
      config.params = {};
    }
    const user = Misc.getUser();
    if (user) {
      config.params.user_id = user.userId;
      config.params.token = user.token;
    }
    return config;
  },
  error => {
    return Promise.reject(error);
  },
);

customAxios.interceptors.response.use(
  response => {
    if (response.data.errorCode === 406 || response.data.errorCode === 407) {
      // token过期或不合法, 重新登录
      Misc.setUser();
      router.push({ name: 'login' });
    }
    return response;
  },
  error => {
    return Promise.reject(error);
  },
);

const http = async <T>(url: string, configs?: AxiosRequestConfig): Promise<HttpResponse<T>> => {
  try {
    const res = await customAxios(url, {
      ...configs,
      baseURL: Misc.getServer()?.http,
    });
    return res.data;
  } catch (error) {
    console.warn(error);
    return {
      errorCode: -1,
      errorMsg: '网络异常',
    };
  }
};

export interface HttpResponse<T> {
  errorCode: number;
  errorMsg?: string;
  data?: T;
}

export default http;
