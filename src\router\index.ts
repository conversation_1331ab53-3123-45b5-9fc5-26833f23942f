import { createRouter, createWebHistory } from 'vue-router';
import Misc from '../script/misc';
const router = createRouter({
  history: createWebHistory(import.meta.env.BASE_URL),
  routes: [
    {
      path: '/',
      name: 'main',
      component: () => import('../views/MainView.vue'),
    },
    {
      path: '/login',
      name: 'login',
      component: () => import('../views/LoginView.vue'),
    },
  ],
});

router.beforeEach((to, from, next) => {
  if (import.meta.env.DEV) {
    console.log('from:', from);
    console.log('to:', to);
  }
  // 未登录跳转到登录页
  if (!Misc.loggedIn() && to.name !== 'login') {
    next({ name: 'login' });
  } else {
    next();
  }
});

export default router;
