/**
 * 成交记录信息
 */
export interface TableTradeRecordInfo {
  /** 成交 ID */
  tradeId: string;
  /** 交易所订单 ID */
  exchangeOrderId: string;
  /** 订单 ID */
  orderId: number;
  /** 用户 ID */
  userId: number;
  /** 用户姓名 */
  userName: string;
  /** 账户 ID */
  accountId: number;
  /** 账户名称 */
  accountName: string;
  /** 基金 ID */
  fundId: number;
  /** 基金名称 */
  fundName: string;
  /** 资产类型 */
  assetType: number;
  /** 交易日 */
  tradingDay: string;
  /** 买卖方向 */
  direction: number;
  /** 开平仓标志 */
  positionEffect: number;
  /** 合约代码 */
  instrument: string;
  /** 合约名称 */
  instrumentName: string;
  /** 成交时间 */
  tradeTime: string;
  /** 成交数量 */
  volume: number;
  /** 成交价格 */
  tradedPrice: number;
}
