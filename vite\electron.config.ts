import { mergeConfig } from 'vite';
import path from 'node:path';
import electron from 'vite-plugin-electron/simple';
import BaseConfig from './vite.config';

export default mergeConfig(BaseConfig, {
  plugins: [
    electron({
      main: {
        entry: 'electron/main.ts',
      },
      preload: {
        input: path.join(__dirname, '../electron/preload.ts'),
      },
      renderer: process.env.NODE_ENV === 'test' ? undefined : {},
    }),
  ],
  define: {
    __PLATFORM__: JSON.stringify('electron'),
  },
  build: {
    outDir: 'dist/electron',
  },
});
