import type { Pool, PoolDetail } from '@/types';

/** 策略池/明细状态 */
export enum TacticStatusEnum {
  新建 = 0,
  运行中,
  已下单,
  补单监控中,
  已停止,
  已删除,
  已完成,
}

/** 策略池类型 */
export enum PoolTypeEnum {
  手动股票池 = 1,
  自动股票池,
}

/** 判断策略池/明细是否已停止 */
export const isStopped = (item: PoolDetail | Pool | null) => {
  if (!item) return true;
  return (
    item.status === TacticStatusEnum.新建 ||
    item.status === TacticStatusEnum.已停止 ||
    item.status === TacticStatusEnum.已删除 ||
    item.status === TacticStatusEnum.已完成
  );
};
