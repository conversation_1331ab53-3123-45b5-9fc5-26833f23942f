export interface AccountInfo {
  /** 账号 ID */
  accountId: number;
  /** 账号名称 */
  accountName: string;
  /** 资产类型 */
  assetType: number;
  /** 可用资金 */
  available: number;
  /** 资金余额 */
  balance: number;
  /** 买入持仓市值 */
  buyMarketValue: number;
  /** 平仓盈亏 */
  closeProfit: number;
  /** 手续费 */
  commission: number;
  /** 是否已连接 */
  connectionStatus: boolean;
  /** 信用账号 */
  credit: boolean;
  /** 当日盘中利润 */
  dayProfit: number;
  /** 资金账号 */
  financeAccount: string;
  /** 归属产品ID */
  fundId: number;
  /** 归属产品名称 */
  fundName: string;
  /** 归属产品份额 */
  fundShare: number;
  /** 冻结保证金 */
  frozenMargin: number;
  /** 冻结手续费 */
  frozenCommission: number;
  /** 账号记录ID */
  id: number | string;
  /** 入金 */
  inMoney: number;
  /** 融资买入市值 */
  loanBuyBalance: number;
  /** 融券卖出市值 */
  loanSellBalance: number;
  /** 可用融券卖出？？ */
  loanSellQuota: number;
  /** 市值 */
  marketValue: number;
  /** 保证金 */
  margin: number;
  /** 最大可用金额 */
  maxLimitMoney: number;
  /** 净值 */
  nav: number;
  /** 出金 */
  outMoney: number;
  /** 持仓盈亏 */
  positionProfit: number;
  /** 上日资金余额 */
  preBalance: number;
  /** 涨跌幅 */
  risePercent: number;
  /** 卖出市值 */
  sellMarketValue: number;
  /** 股值市值 */
  stockIndexMarketValue: number;
  /** 交易日 */
  tradingDay: string;
  /** 可用融资金额 */
  enableCreditBuy: number;
}

export interface AccountField {
  key: {
    [K in keyof AccountInfo]: AccountInfo[K] extends number ? K : never;
  }[keyof AccountInfo];
  label: string;
  format: (value: number) => string;
  /** 是否需要特殊处理颜色 */
  hasColor?: boolean;
}
