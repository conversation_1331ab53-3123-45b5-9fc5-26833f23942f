import { computed, ref, type Ref } from 'vue';

interface options<T> {
  fields?: (keyof T)[];
  customFilter?: (item: T) => boolean;
}

/**
 * 根据查询字符串过滤数据的组合式函数
 * @template T 数据项类型
 * @param {Ref<T[]>} data 原始数据数组的响应式引用
 * @param {object} [options] 可选配置对象
 * @param {(keyof T)[]} [options.fields=['instrument', 'instrumentName']] 需要搜索的字段数组
 * @param {(item: T) => boolean} [options.customFilter] 自定义过滤函数
 */
export const useFilteredData = <T extends Record<string, any>>(
  data: Ref<T[]>,
  options?: options<T>,
) => {
  const query = ref('');

  const filteredData = computed(() => {
    const fields = options?.fields || ['instrument', 'instrumentName'];
    const customFilter = options?.customFilter;

    if (!query.value && !customFilter) {
      return data.value;
    }

    const lowerQuery = query.value.toLowerCase();
    return data.value.filter(item => {
      // 检查指定字段是否包含查询字符串
      const queryMatched = fields.some(field => {
        const value = item[field];
        return typeof value === 'string' && value.toLowerCase().includes(lowerQuery);
      });
      // 如果提供了自定义过滤函数，则需要同时满足自定义过滤条件
      let customMatched = true;
      if (customFilter) {
        customMatched = customFilter(item);
      }
      return queryMatched && customMatched;
    });
  });

  return {
    query,
    filteredData,
  };
};
