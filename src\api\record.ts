import type { TableOrderInfo, TablePositionInfo, TableTradeRecordInfo } from '@/types';
import http from './http';

class RecordService {
  /**
   * 获取持仓
   */
  static getPositions(identityId: number | string) {
    return http<TablePositionInfo[]>('/api/positions', {
      method: 'GET',
      params: {
        identityId,
      },
    });
  }
  /**
   * 获取订单
   */
  static getOrders(identityId: number | string) {
    return http<TableOrderInfo[]>('/api/orders', {
      method: 'GET',
      params: {
        identityId,
      },
    });
  }

  /**
   * 获取成交
   */
  static getTrades(identityId: number | string) {
    return http<TableTradeRecordInfo[]>('/api/trades', {
      method: 'GET',
      params: {
        identityId,
      },
    });
  }
}

export default RecordService;
