<script setup lang="ts">
import type { StrategyLog } from '@/types';
import { shallowRef, watch } from 'vue';
import { useStockSelectionStore } from '@/stores';
import { PoolService } from '@/api';

const logs = shallowRef<StrategyLog[]>([]);
const stockSelectionStore = useStockSelectionStore();

// 获取策略日志数据
const fetchStrategyLogs = async (instrument: string) => {
  const { errorCode, data } = await PoolService.getStrategyLogs(instrument);
  if (errorCode === 0 && data) {
    logs.value = data;
  } else {
    logs.value = [];
  }
};

// 监听选中股票变化
watch(
  () => stockSelectionStore.selectedStock,
  newStock => {
    if (newStock) {
      fetchStrategyLogs(newStock.instrument);
    } else {
      logs.value = [];
    }
  },
  { immediate: true },
);

// 格式化金额显示
const formatAmount = (amount: number) => {
  if (amount >= 10000) {
    return `${(amount / 10000).toFixed(0)}万`;
  }
  return `${amount}元`;
};
</script>

<template>
  <div flex="~ col">
    <div h-32 flex aic px-16 border-b="1 solid [--el-border-color]">
      <span text-14 font-bold>策略日志</span>
    </div>
    <div flex-1 min-h-1>
      <el-scrollbar>
        <!-- 策略日志列表 -->
        <div pt-10 v-if="!stockSelectionStore.selectedStock" flex aic jcc h-full c-gray>
          <span>请选择股票查看策略日志</span>
        </div>
        <div pt-10 v-else-if="logs.length === 0" flex aic jcc h-full c-gray>
          <span>暂无策略日志</span>
        </div>
        <div v-else p-12 flex="~ col" gap-8>
          <div
            v-for="log in logs"
            :key="log.triggerTime"
            p-8
            mb-6
            bg="[--g-panel-bg2]"
            rounded-4
            leading-relaxed
            border-l="3 solid [--g-primary]"
          >
            <div c-white mb-2>
              [{{ log.triggerTime }}] 股票【{{ log.instrumentName }}{{ log.instrument }}】触发{{
                log.triggerType
              }}，触发条件为【{{ log.triggerContent }}】，买入金额为{{ formatAmount(log.amount) }}
            </div>
          </div>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<style scoped></style>
